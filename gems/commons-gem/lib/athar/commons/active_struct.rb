# frozen_string_literal: true

require 'active_model'
require 'securerandom'
require 'athar/commons/active_struct/associations'
require 'athar/commons/active_struct/base'
require 'athar/commons/active_struct/collection'
require 'athar/commons/active_struct/type'
require 'athar/commons/active_struct/active_record_integration'
require 'athar/commons/active_struct/active_record_associations'

module Athar
  module Commons
    # ActiveStruct provides a framework for structured data objects
    # that can be serialized through gRPC but don't have a direct database representation.
    # It allows creating ActiveModel-like objects that can be used with serializers
    # and provides a consistent interface for working with structured data.
    module ActiveStruct
      # Your code goes here...
    end
  end
end
