# frozen_string_literal: true

module Athar
  module Commons
    module ActiveStruct
      # Module to add ActiveStruct association support to ActiveRecord models
      # This allows ActiveRecord models to have belongs_to relationships with ActiveStruct instances
      module ActiveRecordAssociations
        extend ActiveSupport::Concern

        module ClassMethods
          # Define a belongs_to association with an ActiveStruct model
          # This provides Rails-like association syntax for ActiveRecord → ActiveStruct relationships
          #
          # @param name [Symbol] The name of the association
          # @param options [Hash] Association options
          # @option options [String] :foreign_key The foreign key column name (default: "#{name}_id")
          # @option options [String] :class_name The ActiveStruct class name (default: name.classify)
          # @option options [Boolean] :optional Whether the association is optional (default: false)
          #
          # @example
          #   class Case < ApplicationRecord
          #     include Athar::Commons::ActiveStruct::ActiveRecordAssociations
          #     
          #     belongs_to_active_struct :assigned_user, foreign_key: :assigned_user_id, class_name: 'RemoteUser'
          #   end
          def belongs_to_active_struct(name, options = {})
            foreign_key = options[:foreign_key] || "#{name}_id"
            class_name = options[:class_name] || name.to_s.classify
            optional = options.fetch(:optional, false)
            
            # Store association metadata for reflection
            _active_struct_associations[name] = {
              type: :belongs_to_active_struct,
              foreign_key: foreign_key,
              class_name: class_name,
              optional: optional,
              options: options
            }
            
            # Define the association getter method
            define_active_struct_getter(name, foreign_key, class_name)
            
            # Define the association setter method
            define_active_struct_setter(name, foreign_key, class_name)
            
            # Define the build method
            define_active_struct_builder(name, class_name)
            
            # Add validation if not optional
            unless optional
              validates foreign_key, presence: true
            end
          end

          # Get all ActiveStruct associations defined on this model
          def active_struct_associations
            _active_struct_associations
          end

          # Check if an association is an ActiveStruct association
          def active_struct_association?(name)
            _active_struct_associations.key?(name.to_sym)
          end

          private

          # Define the getter method for the ActiveStruct association
          def define_active_struct_getter(name, foreign_key, class_name)
            define_method(name) do
              # Use instance variable for caching
              cache_var = "@_active_struct_#{name}"
              cached_value = instance_variable_get(cache_var)
              
              # Return cached value if foreign key hasn't changed
              current_fk_value = send(foreign_key)
              if cached_value && cached_value.respond_to?(:id) && cached_value.id.to_s == current_fk_value.to_s
                return cached_value
              end
              
              # Clear cache if foreign key changed
              instance_variable_set(cache_var, nil) if cached_value
              
              # Return nil if foreign key is blank
              return nil if current_fk_value.blank?
              
              # Priority 1: Check for existing ActiveRpc method (e.g., assigned_user_data)
              rpc_method = "#{name}_data"
              if respond_to?(rpc_method, true)
                result = send(rpc_method)&.message
                instance_variable_set(cache_var, result) if result
                return result
              end
              
              # Priority 2: Fallback to creating ActiveStruct instance with just the ID
              begin
                klass = class_name.constantize
                result = klass.new(id: current_fk_value)
                instance_variable_set(cache_var, result)
                result
              rescue NameError => e
                if defined?(Rails) && Rails.respond_to?(:logger)
                  Rails.logger.warn "ActiveStruct association #{name}: Could not find class #{class_name}: #{e.message}"
                end
                nil
              end
            end
          end

          # Define the setter method for the ActiveStruct association
          def define_active_struct_setter(name, foreign_key, class_name)
            define_method("#{name}=") do |value|
              cache_var = "@_active_struct_#{name}"
              if value.nil?
                # Clear the association
                send("#{foreign_key}=", nil)
                instance_variable_set(cache_var, nil)
              elsif value.respond_to?(:id)
                # Set from ActiveStruct instance
                send("#{foreign_key}=", value.id)
                instance_variable_set(cache_var, value)
              elsif value.is_a?(Hash)
                # Create new instance from hash
                begin
                  klass = class_name.constantize
                  instance = klass.new(value)
                  send("#{foreign_key}=", instance.id)
                  instance_variable_set(cache_var, instance)
                rescue NameError => e
                  raise ArgumentError, "Could not find class #{class_name}: #{e.message}"
                end
              else
                # Check if it's an instance of the expected class (more flexible check)
                begin
                  klass = class_name.constantize
                  if value.is_a?(klass)
                    send("#{foreign_key}=", value.id)
                    instance_variable_set(cache_var, value)
                  else
                    raise ArgumentError, "Expected #{class_name} instance, Hash, or nil, got #{value.class}"
                  end
                rescue NameError => e
                  raise ArgumentError, "Expected #{class_name} instance, Hash, or nil, got #{value.class}"
                end
              end
            end
          end

          # Define the build method for creating new associated instances
          def define_active_struct_builder(name, class_name)
            define_method("build_#{name}") do |attributes = {}|
              begin
                klass = class_name.constantize
                instance = klass.new(attributes)
                send("#{name}=", instance)
                instance
              rescue NameError => e
                raise ArgumentError, "Could not find class #{class_name}: #{e.message}"
              end
            end
          end
        end

        included do
          # Initialize the associations hash
          class_attribute :_active_struct_associations, instance_writer: false, default: {}
        end

        # Instance methods for association management
        # Clear all ActiveStruct association caches
        def clear_active_struct_association_cache
          self.class._active_struct_associations.each_key do |name|
            instance_variable_set("@_active_struct_#{name}", nil)
          end
        end

        # Get the cached value for an ActiveStruct association
        def active_struct_association_cached?(name)
          instance_variable_get("@_active_struct_#{name}").present?
        end

        # Reload an ActiveStruct association (clear cache and re-fetch)
        def reload_active_struct_association(name)
          instance_variable_set("@_active_struct_#{name}", nil)
          send(name)
        end
      end
    end
  end
end
