# AtharProcure - Complete System Architecture Plan

## 🎯 Executive Summary

**AtharProcure** is a comprehensive procurement management system designed to handle the complete procurement lifecycle from item catalog management to purchase order fulfillment. Built as part of the Athar EMS ecosystem, it follows project-based multi-tenancy patterns similar to the case-manager system.

## 🏗️ System Architecture Overview

### Core Principles
- **Project-Based Multi-Tenancy**: All data scoped by `project_id`
- **Role-Based Access Control**: Granular permissions using AtharAuth
- **Approval Workflows**: Integration with Athar::Commons approval system
- **API-First Design**: RESTful APIs with comprehensive documentation
- **Microservice Integration**: gRPC communication with other Athar services

### Technology Stack
- **Backend**: Ruby on Rails 8.0 (API-only)
- **Database**: PostgreSQL with UUID primary keys
- **Authentication**: AtharAuth gem with JWT tokens
- **Documentation**: Apipie-rails for auto-generated API docs
- **Testing**: RSpec with FactoryBot
- **Serialization**: JSONAPI-Serializer for consistent API responses

## 📊 High-Level Data Model

### Core Entities

#### 1. **User** (Inherited from AtharAuth)
```ruby
class User < AtharAuth::Models::User
  # Inherited: id, name, email, project_id, role, permissions
  
  # Procurement-specific associations
  has_many :procurement_requests, foreign_key: :requester_id
  has_many :approved_requests, foreign_key: :approver_id
  has_many :created_items, foreign_key: :created_by_id
  has_many :managed_suppliers, foreign_key: :manager_id
  has_many :purchase_orders, foreign_key: :requester_id
  
  # Role-based access methods
  def procurement_manager?
  def procurement_officer?
  def finance_manager?
  def accountant?
end
```

#### 2. **Item** (Procurement Catalog)
```ruby
class Item < ApplicationRecord
  # Core attributes
  belongs_to :creator, class_name: "User", foreign_key: :created_by_id
  belongs_to :category, class_name: "ItemCategory"
  has_many :procurement_requests
  has_many :purchase_order_items
  
  # Project scoping
  validates :project_id, presence: true
  scope :for_project, ->(project_id) { where(project_id: project_id) }
  
  # Attributes: name, description, category_id, approx_price, 
  #            vendor_name, specifications, project_id
end
```

#### 3. **ProcurementRequest** (Employee Requests)
```ruby
class ProcurementRequest < ApplicationRecord
  include Athar::Commons::Models::Concerns::ActsAsApprovable
  
  belongs_to :item
  belongs_to :requester, class_name: "User"
  belongs_to :approver, class_name: "User", optional: true
  has_many :procurement_request_items
  has_many :comments, as: :commentable
  
  # Approval workflow integration
  acts_as_approvable
  
  # Status management
  enum status: {
    draft: "draft",
    submitted: "submitted", 
    under_review: "under_review",
    approved: "approved",
    rejected: "rejected",
    cancelled: "cancelled"
  }
end
```

#### 4. **Supplier** (Vendor Management)
```ruby
class Supplier < ApplicationRecord
  belongs_to :manager, class_name: "User", foreign_key: :manager_id
  has_many :purchase_orders
  has_many :supplier_items
  has_many :items, through: :supplier_items
  
  # Project scoping
  validates :project_id, presence: true
  scope :for_project, ->(project_id) { where(project_id: project_id) }
  
  # Attributes: name, contact_person, email, phone, address,
  #            tax_number, payment_terms, project_id
end
```

#### 5. **PurchaseOrder** (Purchase Management)
```ruby
class PurchaseOrder < ApplicationRecord
  include Athar::Commons::Models::Concerns::ActsAsApprovable
  
  belongs_to :supplier
  belongs_to :requester, class_name: "User"
  belongs_to :approver, class_name: "User", optional: true
  has_many :purchase_order_items
  has_many :procurement_requests
  
  # Financial tracking
  monetize :total_amount_cents
  
  # Status workflow
  enum status: {
    draft: "draft",
    pending_approval: "pending_approval",
    approved: "approved",
    sent_to_supplier: "sent_to_supplier",
    partially_received: "partially_received",
    completed: "completed",
    cancelled: "cancelled"
  }
end
```

## 🔐 User Roles & Permissions

### Role Hierarchy
1. **Global Users**: Full system access across all projects
2. **Procurement Managers**: Manage procurement within their project
3. **Procurement Officers**: Handle day-to-day procurement operations
4. **Finance Managers**: Financial oversight and budget approval
5. **Accountants**: Financial tracking and reporting
6. **Employees**: Submit procurement requests

### Permission Matrix
```ruby
# Permission format: "action:resource"
PERMISSIONS = {
  # Items
  "read:item" => %w[all_roles],
  "create:item" => %w[procurement_manager procurement_officer],
  "update:item" => %w[procurement_manager procurement_officer],
  "destroy:item" => %w[procurement_manager],
  
  # Procurement Requests
  "read:procurement_request" => %w[all_roles],
  "create:procurement_request" => %w[all_roles],
  "update:procurement_request" => %w[procurement_manager procurement_officer requester],
  "approve:procurement_request" => %w[procurement_manager],
  
  # Suppliers
  "read:supplier" => %w[procurement_manager procurement_officer finance_manager],
  "create:supplier" => %w[procurement_manager],
  "update:supplier" => %w[procurement_manager],
  
  # Purchase Orders
  "read:purchase_order" => %w[procurement_manager procurement_officer finance_manager],
  "create:purchase_order" => %w[procurement_manager procurement_officer],
  "approve:purchase_order" => %w[procurement_manager finance_manager]
}
```

## 🚀 API Endpoints Overview

### Core Resource APIs

#### Items Management
```
GET    /api/items                    # List items (project-scoped)
POST   /api/items                    # Create new item
GET    /api/items/:id                # Get item details
PATCH  /api/items/:id                # Update item
DELETE /api/items/:id                # Delete item
GET    /api/items/categories         # List item categories
```

#### Procurement Requests
```
GET    /api/procurement_requests     # List requests (role-based filtering)
POST   /api/procurement_requests     # Create new request
GET    /api/procurement_requests/:id # Get request details
PATCH  /api/procurement_requests/:id # Update request
DELETE /api/procurement_requests/:id # Cancel request
POST   /api/procurement_requests/:id/submit    # Submit for approval
POST   /api/procurement_requests/:id/approve   # Approve request
POST   /api/procurement_requests/:id/reject    # Reject request
```

#### Suppliers Management
```
GET    /api/suppliers                # List suppliers (project-scoped)
POST   /api/suppliers                # Create new supplier
GET    /api/suppliers/:id            # Get supplier details
PATCH  /api/suppliers/:id            # Update supplier
DELETE /api/suppliers/:id            # Delete supplier
GET    /api/suppliers/:id/items      # Get supplier's items
```

#### Purchase Orders
```
GET    /api/purchase_orders          # List purchase orders
POST   /api/purchase_orders          # Create new PO
GET    /api/purchase_orders/:id      # Get PO details
PATCH  /api/purchase_orders/:id      # Update PO
POST   /api/purchase_orders/:id/approve     # Approve PO
POST   /api/purchase_orders/:id/send       # Send to supplier
POST   /api/purchase_orders/:id/receive    # Mark as received
```

### Analytics & Reporting APIs
```
GET    /api/analytics/dashboard      # Procurement dashboard data
GET    /api/analytics/spending       # Spending analysis
GET    /api/analytics/suppliers      # Supplier performance
GET    /api/reports/procurement      # Generate procurement reports
```

## 🔄 Workflow Processes

### 1. Procurement Request Workflow
```
Employee Request → Manager Review → Approval → Purchase Order → Fulfillment
```

### 2. Item Catalog Management
```
Manager Creates Item → Categorization → Approval → Available for Requests
```

### 3. Supplier Onboarding
```
Supplier Registration → Verification → Approval → Active Supplier
```

### 4. Purchase Order Process
```
PO Creation → Financial Approval → Supplier Notification → Delivery → Receipt Confirmation
```

## 🎨 Frontend Integration Points

### Dashboard Components
- **Procurement Overview**: Key metrics and pending actions
- **Request Management**: Submit and track procurement requests
- **Approval Queue**: Pending approvals for managers
- **Supplier Directory**: Supplier information and performance
- **Purchase Order Tracking**: PO status and delivery tracking

### User Experience Flows
- **Employee Flow**: Browse catalog → Submit request → Track status
- **Manager Flow**: Review requests → Approve/reject → Monitor budgets
- **Finance Flow**: Budget oversight → Payment tracking → Financial reports

## 📈 Integration with Athar Ecosystem

### Core Service Integration
- **User Management**: Centralized user authentication and roles
- **Project Management**: Project-based data isolation
- **Permission System**: Unified authorization across services

### Case Manager Integration
- **Shared Approval Patterns**: Similar workflow management
- **Document Handling**: Consistent file management approach
- **Comment System**: Unified commenting across services

### People Service Integration
- **Employee Data**: Staff information for procurement requests
- **Budget Integration**: Department budget tracking
- **Approval Hierarchies**: Manager-employee relationships

## 🔧 Technical Implementation Notes

### Database Design
- **UUID Primary Keys**: Consistent with ecosystem standards
- **Project Scoping**: All tables include `project_id` for multi-tenancy
- **Audit Trails**: Track all changes with timestamps and user IDs
- **Soft Deletes**: Maintain data integrity for historical records

### Performance Considerations
- **Database Indexing**: Proper indexes on project_id, status, dates
- **Caching Strategy**: Redis caching for frequently accessed data
- **Background Jobs**: Async processing for notifications and reports
- **API Pagination**: Efficient data loading for large datasets

### Security Measures
- **JWT Authentication**: Secure token-based authentication
- **Role-Based Authorization**: Granular permission checking
- **Data Encryption**: Sensitive data encryption at rest
- **Audit Logging**: Comprehensive activity logging

## 🚀 Next Steps for Implementation

1. **Phase 1**: Core models and basic CRUD operations
2. **Phase 2**: Approval workflow integration
3. **Phase 3**: Advanced features (analytics, reporting)
4. **Phase 4**: Frontend integration and user experience
5. **Phase 5**: Performance optimization and scaling

This architecture plan provides a comprehensive foundation for building a robust, scalable procurement management system that integrates seamlessly with the Athar EMS ecosystem.
