#!/usr/bin/env ruby
# frozen_string_literal: true

require 'bundler/setup'
require 'active_model'
require 'active_support/all'

# Load our commons gem
$LOAD_PATH.unshift File.expand_path('gems/commons-gem/lib')
require 'athar/commons/active_struct'

# Test ActiveStruct model
class TestUser < Athar::Commons::ActiveStruct::Base
  attribute :id, :integer
  attribute :name, :string
  attribute :email, :string
end

# Mock ActiveRecord
class MockActiveRecord
  include ActiveModel::Model
  include ActiveModel::Attributes
  include Athar::Commons::ActiveStruct::ActiveRecordAssociations
  
  attribute :id, :integer
  attribute :assigned_user_id, :integer
end

# Test model
class TestCase < MockActiveRecord
  belongs_to_active_struct :assigned_user, foreign_key: :assigned_user_id, class_name: 'TestUser'
end

puts "Testing hash assignment..."

test_case = TestCase.new
puts "Before assignment: assigned_user_id = #{test_case.assigned_user_id}"

# Test hash assignment step by step
hash_data = { id: 789, name: '<PERSON>' }
puts "Hash data: #{hash_data.inspect}"

# Create user manually to test
manual_user = TestUser.new(hash_data)
puts "Manual user: id=#{manual_user.id}, name=#{manual_user.name}"

# Now test assignment
test_case.assigned_user = hash_data
puts "After assignment: assigned_user_id = #{test_case.assigned_user_id}"
puts "Retrieved user: id=#{test_case.assigned_user.id}, name=#{test_case.assigned_user.name}"

# Test caching
user1 = test_case.assigned_user
user2 = test_case.assigned_user
puts "Caching test: same object? #{user1.equal?(user2)}"
