#!/usr/bin/env ruby
# frozen_string_literal: true

require 'bundler/setup'
require 'active_model'
require 'active_support/all'

# Load our commons gem
$LOAD_PATH.unshift File.expand_path('gems/commons-gem/lib')
require 'athar/commons/active_struct'

# Test ActiveStruct model
class TestUser < Athar::Commons::ActiveStruct::Base
  attribute :id, :integer
  attribute :name, :string
  attribute :email, :string
end

# Mock ActiveRecord
class MockActiveRecord
  include ActiveModel::Model
  include ActiveModel::Attributes
  include Athar::Commons::ActiveStruct::ActiveRecordAssociations
  
  attribute :id, :integer
  attribute :assigned_user_id, :integer
end

# Test model
class TestCase < MockActiveRecord
  belongs_to_active_struct :assigned_user, foreign_key: :assigned_user_id, class_name: 'TestUser'
end

puts "Testing basic functionality..."

# Test 1: Basic getter
test_case = TestCase.new(assigned_user_id: 123)
user = test_case.assigned_user
puts "✅ Basic getter: #{user.class} with ID #{user.id}"

# Test 2: Setter with instance
user_instance = TestUser.new(id: 456, name: '<PERSON>')
test_case.assigned_user = user_instance
puts "✅ Setter with instance: assigned_user_id = #{test_case.assigned_user_id}"

# Test 3: Hash assignment
test_case.assigned_user = { id: 789, name: 'Jane Smith' }
puts "✅ Hash assignment: name = #{test_case.assigned_user.name}"

# Test 4: Build method
built_user = test_case.build_assigned_user(id: 999, name: 'Built User')
puts "✅ Build method: #{built_user.name}"

puts "\nAll basic tests passed! ✅"
