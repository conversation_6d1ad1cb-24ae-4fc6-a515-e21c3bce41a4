#!/usr/bin/env ruby
# frozen_string_literal: true

# Simple test script to verify ActiveStruct Associations functionality
# This runs outside of Rails to avoid database connection issues

require 'bundler/setup'
require 'active_model'
require 'active_support/all'

# Load our commons gem
$LOAD_PATH.unshift File.expand_path('gems/commons-gem/lib')
require 'athar/commons/active_struct'

# Mock ActiveRecord::Base for testing
class MockActiveRecord
  include ActiveModel::Model
  include ActiveModel::Attributes
  include Athar::Commons::ActiveStruct::ActiveRecordAssociations
  
  attribute :id, :integer
  attribute :assigned_user_id, :integer
  attribute :created_by_id, :integer
  
  def self.validates(*args)
    # Mock validation - just store for testing
    @validations ||= []
    @validations << args
  end
  
  def self.validations
    @validations || []
  end
  
  def valid?
    true # Simplified for testing
  end
end

# Test ActiveStruct model
class TestUser < Athar::Commons::ActiveStruct::Base
  attribute :id, :integer
  attribute :name, :string
  attribute :email, :string
end

# Test ActiveRecord model with associations
class TestCase < MockActiveRecord
  belongs_to_active_struct :assigned_user, foreign_key: :assigned_user_id, class_name: 'TestUser'
  belongs_to_active_struct :created_by_user, foreign_key: :created_by_id, class_name: 'TestUser'
end

# Test runner
class TestRunner
  def initialize
    @tests_passed = 0
    @tests_failed = 0
  end
  
  def test(description, &block)
    print "Testing: #{description}... "
    begin
      instance_eval(&block)
      puts "✅ PASSED"
      @tests_passed += 1
    rescue => e
      puts "❌ FAILED: #{e.message}"
      puts "  #{e.backtrace.first}"
      @tests_failed += 1
    end
  end
  
  def assert(condition, message = "Assertion failed")
    raise message unless condition
  end
  
  def assert_equal(expected, actual, message = nil)
    message ||= "Expected #{expected.inspect}, got #{actual.inspect}"
    assert(expected == actual, message)
  end
  
  def assert_respond_to(object, method, message = nil)
    message ||= "Expected #{object.class} to respond to #{method}"
    assert(object.respond_to?(method), message)
  end
  
  def assert_kind_of(klass, object, message = nil)
    message ||= "Expected #{object.inspect} to be a kind of #{klass}"
    assert(object.is_a?(klass), message)
  end
  
  def summary
    total = @tests_passed + @tests_failed
    puts "\n" + "="*50
    puts "Test Summary:"
    puts "  Total: #{total}"
    puts "  Passed: #{@tests_passed}"
    puts "  Failed: #{@tests_failed}"
    puts "  Success Rate: #{(@tests_passed.to_f / total * 100).round(1)}%"
    puts "="*50
    
    @tests_failed == 0
  end
end

# Run the tests
runner = TestRunner.new

runner.test "Module inclusion" do
  assert(TestCase.included_modules.include?(Athar::Commons::ActiveStruct::ActiveRecordAssociations))
end

runner.test "Association metadata creation" do
  associations = TestCase.active_struct_associations
  assert(associations.key?(:assigned_user))
  assert_equal(:belongs_to_active_struct, associations[:assigned_user][:type])
  assert_equal(:assigned_user_id, associations[:assigned_user][:foreign_key])
  assert_equal('TestUser', associations[:assigned_user][:class_name])
end

runner.test "Association method creation" do
  test_case = TestCase.new
  assert_respond_to(test_case, :assigned_user)
  assert_respond_to(test_case, :assigned_user=)
  assert_respond_to(test_case, :build_assigned_user)
end

runner.test "Basic association getter" do
  test_case = TestCase.new(assigned_user_id: 123)
  user = test_case.assigned_user
  assert_kind_of(TestUser, user)
  assert_equal(123, user.id)
end

runner.test "Association setter with ActiveStruct instance" do
  test_case = TestCase.new
  user = TestUser.new(id: 456, name: 'John Doe')
  test_case.assigned_user = user
  
  assert_equal(456, test_case.assigned_user_id)
  assert_equal(user, test_case.assigned_user)
end

runner.test "Association setter with nil" do
  test_case = TestCase.new(assigned_user_id: 123)
  test_case.assigned_user = nil
  
  assert_equal(nil, test_case.assigned_user_id)
  assert_equal(nil, test_case.assigned_user)
end

runner.test "Association setter with hash" do
  test_case = TestCase.new
  test_case.assigned_user = { id: 789, name: 'Jane Smith' }
  
  assert_equal(789, test_case.assigned_user_id)
  assert_equal('Jane Smith', test_case.assigned_user.name)
end

runner.test "Build method" do
  test_case = TestCase.new
  user = test_case.build_assigned_user(id: 999, name: 'Built User')
  
  assert_kind_of(TestUser, user)
  assert_equal('Built User', user.name)
  assert_equal(user, test_case.assigned_user)
  assert_equal(999, test_case.assigned_user_id)
end

runner.test "Association caching" do
  test_case = TestCase.new(assigned_user_id: 123)
  user1 = test_case.assigned_user
  user2 = test_case.assigned_user
  
  # Should be the same object reference (cached)
  assert(user1.equal?(user2), "Expected same object reference for cached association")
end

runner.test "Cache invalidation on foreign key change" do
  test_case = TestCase.new(assigned_user_id: 123)
  user1 = test_case.assigned_user
  
  test_case.assigned_user_id = 456
  user2 = test_case.assigned_user
  
  # Should be different objects
  assert(!user1.equal?(user2), "Expected different objects after foreign key change")
  assert_equal(456, user2.id)
end

runner.test "Association existence check" do
  assert(TestCase.active_struct_association?(:assigned_user))
  assert(TestCase.active_struct_association?('assigned_user'))
  assert(!TestCase.active_struct_association?(:nonexistent))
end

runner.test "Cache management methods" do
  test_case = TestCase.new(assigned_user_id: 123, created_by_id: 456)
  
  # Load associations to cache them
  test_case.assigned_user
  test_case.created_by_user
  
  assert(test_case.active_struct_association_cached?(:assigned_user))
  
  # Clear cache
  test_case.clear_active_struct_association_cache
  assert(!test_case.active_struct_association_cached?(:assigned_user))
end

runner.test "Reload association" do
  test_case = TestCase.new(assigned_user_id: 123)
  user1 = test_case.assigned_user
  user2 = test_case.reload_active_struct_association(:assigned_user)
  
  # Should be different object references but same data
  assert(!user1.equal?(user2), "Expected different objects after reload")
  assert_equal(user1.id, user2.id)
end

runner.test "Error handling for invalid assignment" do
  test_case = TestCase.new
  
  begin
    test_case.assigned_user = "invalid"
    assert(false, "Expected ArgumentError to be raised")
  rescue ArgumentError => e
    assert(e.message.include?("Expected TestUser instance"), "Expected specific error message")
  end
end

runner.test "ActiveRpc integration simulation" do
  # Create a test case with a mock ActiveRpc method
  test_case = TestCase.new(assigned_user_id: 123)
  
  # Define a mock ActiveRpc method
  def test_case.assigned_user_data
    double_response = Object.new
    def double_response.message
      TestUser.new(id: 123, name: 'RPC User', email: '<EMAIL>')
    end
    double_response
  end
  
  user = test_case.assigned_user
  assert_equal('RPC User', user.name)
  assert_equal('<EMAIL>', user.email)
end

# Run summary
success = runner.summary

exit(success ? 0 : 1)
