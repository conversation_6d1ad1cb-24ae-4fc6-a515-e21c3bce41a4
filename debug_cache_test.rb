#!/usr/bin/env ruby
# frozen_string_literal: true

require 'bundler/setup'
require 'active_model'
require 'active_support/all'

# Load our commons gem
$LOAD_PATH.unshift File.expand_path('gems/commons-gem/lib')
require 'athar/commons/active_struct'

# Test ActiveStruct model
class TestUser < Athar::Commons::ActiveStruct::Base
  attribute :id, :integer
  attribute :name, :string
  attribute :email, :string
end

# Mock ActiveRecord
class MockActiveRecord
  include ActiveModel::Model
  include ActiveModel::Attributes
  include Athar::Commons::ActiveStruct::ActiveRecordAssociations
  
  attribute :id, :integer
  attribute :assigned_user_id, :integer
end

# Test model with debugging
class TestCase < MockActiveRecord
  belongs_to_active_struct :assigned_user, foreign_key: :assigned_user_id, class_name: 'TestUser'
  
  # Override the getter to add debugging
  def assigned_user
    cache_var = "@_active_struct_assigned_user"
    cached_value = instance_variable_get(cache_var)
    current_fk_value = assigned_user_id
    
    puts "DEBUG: cache_var=#{cache_var}"
    puts "DEBUG: cached_value=#{cached_value.inspect}"
    puts "DEBUG: current_fk_value=#{current_fk_value}"
    
    if cached_value
      puts "DEBUG: cached_value.id=#{cached_value.id}"
      puts "DEBUG: comparison: #{cached_value.id.to_s} == #{current_fk_value.to_s} => #{cached_value.id.to_s == current_fk_value.to_s}"
    end
    
    # Call the original method
    super
  end
end

puts "Testing caching with debugging..."

test_case = TestCase.new

# Set with hash
puts "\n=== Setting with hash ==="
test_case.assigned_user = { id: 789, name: 'Jane Smith' }

puts "\n=== First retrieval ==="
user1 = test_case.assigned_user

puts "\n=== Second retrieval ==="
user2 = test_case.assigned_user

puts "\nFinal result: same object? #{user1.equal?(user2)}"
